﻿
function LoadLanguages() {

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'LoadListLanguages',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
            $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
        },
        success: function (data) {
            $('#loading').html('');

            if (data.d.length > 0) {
                var optionsLanguages = $("#selectLang");
                //don't forget error handling!
                $.each(data.d, function (indx, item) {
                    optionsLanguages.append($("<option />").val(item.LanguageCode).text(item.LanguageName));
                });
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message, "alert alert-danger alert-dismissable");
            console.log(XMLHttpRequest.responseJSON.Message);
            $('#loading').html('');
        }
    });
}


var isWriteTextHaut = false;
var isWriteTextBas = false;
function ChangeContent(sAjaxSourceUrl, urlId, pageName) {

    //        var pageName = $("#myTab li.active a").data('id');
    //        var urlId = $("#myTab li.active a").attr('href');

    if (isWriteTextHaut || isWriteTextBas) {

       var pageNameCurrent = $("#myTab li.active a").data('id');
        var result = window.confirm("Voulez vous enregistrer avant de changer d'onglet ?");
        if (result == true) {
            if (isWriteTextHaut) {
                SaveContent(pageName, "haut", pageNameCurrent);
                isWriteTextHaut = false;

            }

            if (isWriteTextBas) {
                SaveContent(pageName, "bas", pageNameCurrent);
                isWriteTextBas = false;
            }

           // SaveContent(pageName, direction)
        }
    } 


        var sData = JSON.stringify({ _pageName: pageName, _divName: urlId, _ddlLang: ddlLang, _appKey: $('.tabbable').data('key') });

        $.ajax({
            type: "POST",
            url: sAjaxSourceUrl + 'GetContentPage',
            data: sData,
            async: false,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            beforeSend: function () {
                var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
                $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
            },
            success: function (response) {
                $('#loading').html('');
                $("#content").html("");
                $("#content").html(response.d);

                $("#EnregistrerHaut").on('click', function () {
                    SaveContent(pageName, "haut", "");
                });

                $("#EnregistrerBas").on('click', function () {
                    var text = $("#wysieditor_abo_formule2 div").html();
                    SaveContent(pageName, "bas", "");
                });

                $("#GetLastFileHaut").on('click', function () {
                    GetLastFile(pageName, "haut", "");
                });

                $("#GetLastFileBas").on('click', function () {
                    GetLastFile(pageName, "bas", "");
                });

                $("#wysieditor_abo_formule1").on("change keyup paste", function () {
                    isWriteTextHaut = true;
                });


                $("#wysieditor_abo_formule2").on("change keyup paste", function () {
                    isWriteTextBas = true;
                });

               
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                ShowError("error", XMLHttpRequest.responseJSON.Message, "alert alert-danger alert-dismissable");
                console.log(XMLHttpRequest.responseJSON.Message);
                $('#loading').html('');
            }
        });
    
    
}
//direction = haut ou bas
function GetLastFile(pageName, direction) {

    var text = "";

    // Récupérer les mêmes paramètres que pour SaveContent
    var buyerProfilId = 0;
    if($('#selectBuyerProfil').length > 0){
       if ($('#selectBuyerProfil option:selected').val() != undefined)
           buyerProfilId = $('#selectBuyerProfil').val();
    }

    var arrSeances = GetSeances();
    var thisManifGroupId = $('#select_manifs_groups option:selected').val();
    var thisManifId = $('#select_manifs option:selected').val();

    var sData = JSON.stringify({ _pageName: pageName, _direction: direction, _ddlLang: ddlLang, _appKey: $('.tabbable').data('key'), _manifId: thisManifId, _arrSeancesId: arrSeances, _eventGroupId: thisManifGroupId, _buyerProfilId: buyerProfilId });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'GetLastFile',
        data: sData,
        async: false,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
            $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
        },
        success: function (response) {
            $('#loading').html('');
            if (direction == "haut") {
                $("#wysieditor_abo_formule1").html(response.d);
            } else {
                $("#wysieditor_abo_formule2").html(response.d);
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message, "alert alert-danger alert-dismissable");
            console.log(XMLHttpRequest.responseJSON.Message);
            $('#loading').html('');
        }
    });

}

//direction = haut ou bas
function SaveContent(pageName, direction, pageNameCurrent) {

    if (pageNameCurrent != "") {
       pageName = $("#myTab li.active a").data('id');

    }

    var text = "";
    if (direction == "haut") {
        text = $("#wysieditor_abo_formule1").html();
    } else {
        text = $("#wysieditor_abo_formule2").html();
    }

    var valid_links_regex = "MsoNormal";

    var re = new RegExp(valid_links_regex, 'ig')

    if (text.match(re)) {
        ShowError("error", $('#lblErrorCopyMotsWord').text(), "alert alert-danger alert-dismissable");

    } else {

        var sData = JSON.stringify({ _pageName: pageName, _text: text, _direction: direction, _ddlLang: ddlLang, _appKey: $('.tabbable').data('key') });


        $.ajax({
            type: "POST",
            url: sAjaxSourceUrl + 'SaveContent',
            data: sData,
            async: false,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            beforeSend: function () {
                var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
                $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
            },
            success: function (response) {
                $('#loading').html('');

                //mettre une belle popup pour afficher que c'est sauvegarder
                ShowError("error", response.d.split(':')[1], "alert alert-" + response.d.split(':')[0] + " alert-dismissable");

            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                ShowError("error", XMLHttpRequest.responseJSON.Message, "alert alert-danger alert-dismissable");
                console.log(XMLHttpRequest.responseJSON.Message);
                $('#loading').html('');
            }
        });

    }

}




function fnOpenNormalDialog() {
    $("#dialog-confirm").html("Confirm Dialog Box");

    // Define the Dialog and its properties.
    $("#dialog-confirm").dialog({
        resizable: false,
        modal: true,
        title: "Modal",
        height: 250,
        width: 400,
        buttons: {
            "Yes": function () {
                $(this).dialog('close');
                callback(true);
            },
            "No": function () {
                $(this).dialog('close');
                callback(false);
            }
        }
    });
}