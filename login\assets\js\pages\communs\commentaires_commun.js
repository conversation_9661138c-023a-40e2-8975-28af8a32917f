﻿
function LoadListSeances(manifId) {

    if (manifId != undefined) {
        var platformName = $("#myTab li.active a").data('key');

        var sData = JSON.stringify({ _manifId: manifId, _appKey: platformName });

        $.ajax({
            type: "POST",
            url: sAjaxSourceUrl + 'LoadListSeances',
            data: sData,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            async: false,
            success: function (response) {
                if (response != null) {
                    $('#select_seances').children().remove();
                    var options = $("#select_seances");
                    options.append($("<option />").val(0).text(ReadXmlTranslate("select_one_seance")));

                    $.each(response.d, function () {
                        options.append($("<option />").val(this.sessionId).text(this.sSessionStartDate));
                    });
                }
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
                console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
            }
        });
    }  
}


function GetSeances() {
    var arrSeances = new Array();
    $('#select_seances option:selected').each(function (i, itm) {


        var OSessionEntity = {
            SessionId: itm.value,
            sSessionStartDate: itm.text
        };

      /*  var obj = new Object();
        obj.SessionId = itm.value;
        obj.sSessionStartDate = itm.text;
        */
        arrSeances.push(OSessionEntity);
       // arrSeances.push({ id: itm.value, name: itm.text });
    });

    return arrSeances;
}





function LoadListManifestationsGroups() {
    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'LoadListManifestationsGroups',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        async: false,
        success: function (response) {

            var options = $("#select_manifs_groups");
            options.append($("<option />").val(0).text(ReadXmlTranslate("select_one_manifestation_group")));
            $.each(response.d, function () {
                options.append($("<option />").val(this.eventGroupID).text(this.eventGroupName));
            });
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }

    });
}



function LoadListManifestations() {

    var eventGroupIdSelected = $('#select_manifs_groups').val();
    var sData = JSON.stringify({ _eventGroupIdSelected: eventGroupIdSelected });
    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'LoadListManifestations',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        data: sData,
        async: false,
        success: function (response) {

            $("#select_manifs").empty();
            if (response.d.length > 0) {

                
                var options = $("#select_manifs");
                options.append($("<option />").val(0).text(ReadXmlTranslate("select_one_manifestation")));
                $.each(response.d, function () {
                    options.append($("<option />").val(this.EventId).text(this.EventName));
                });
            } 

          
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message + "<br />" + XMLHttpRequest.responseJSON.StackTrace + "\r\n", "alert alert-danger alert-dismissable", 5000);
            console.log(XMLHttpRequest.responseJSON.Message + "\r\n" + XMLHttpRequest.responseJSON.StackTrace);
        }

    });
}


function ConfirmMessage(pageName, direction, pageNameCurrent) {

   // $("#"+id).confirm({

    $.confirm({
        // text: "Etes-vous sûr de vouloir récupérer le dernier fichier ? </br> Cette action entrainera une perte du travail en cours.",
        text: ReadXmlTranslate("confirm_msg_recup_last_file"),
        title: "Confirmation",
        confirm: function () {
            GetLastFile(pageName, direction, pageNameCurrent);
        },
        cancel: function (button) {
            // do something
        },
        confirmButton: ReadXmlTranslate("valider"),
        cancelButton: ReadXmlTranslate("annuler"),
        post: true
    });

}



function LoadLanguages() {

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'LoadListLanguages',
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (data) {

            if (data.d != null) {
                var optionsLanguages = $("#selectLang");
                //don't forget error handling!
                $.each(data.d, function (indx, item) {
                    optionsLanguages.append($("<option />").val(item.LanguageCode).text(item.LanguageName));
                });
            } else {
                ShowError("error", ReadXmlTranslate('no_langage_in_commentaire'), "alert alert-danger alert-dismissable");         
                $('#div_select_language').hide();
            }
        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message, "alert alert-danger alert-dismissable");
            console.log(XMLHttpRequest.responseJSON.Message);
            $('#loading').html('');
        }
    });
}


var isWriteTextHaut = false;
var isWriteTextBas = false;
function ChangeContent(sAjaxSourceUrl, urlId, pageName, manifId, seanceId, eventGroupId, platformName, buyerProfilId) {

    //        var pageName = $("#myTab li.active a").data('id');
    //        var urlId = $("#myTab li.active a").attr('href');

    if (isWriteTextHaut || isWriteTextBas) {

       var pageNameCurrent = $("#myTab li.active a").data('id');
       var result = window.confirm(ReadXmlTranslate("msg_change_tabs"));
        if (result == true) {
            if (isWriteTextHaut) {
                SaveContent(pageName, "haut", pageNameCurrent);               
            }

            if (isWriteTextBas) {
                SaveContent(pageName, "bas", pageNameCurrent);
            }

           // SaveContent(pageName, direction)
        }

        isWriteTextHaut = false;
        isWriteTextBas = false;

    }
    //var platformName = $("#myTab li.active").data('key');
    var platformName = $("#myTab li.active a").data('key');


    var sData = "";
    if (seanceId > 0)
        sData = JSON.stringify({ _pageName: pageName, _divName: urlId, _ddlLang: ddlLang, _appKey: platformName, _manifId: manifId, _seanceId: seanceId, _eventGroupId: eventGroupId, _buyerProfilId: buyerProfilId  });
    else if (manifId > 0) {
        sData = JSON.stringify({ _pageName: pageName, _divName: urlId, _ddlLang: ddlLang, _appKey: platformName, _manifId: manifId, _seanceId: 0, _eventGroupId: eventGroupId, _buyerProfilId: buyerProfilId  });
    } else {
        sData = JSON.stringify({ _pageName: pageName, _divName: urlId, _ddlLang: ddlLang, _appKey: platformName, _manifId: 0, _seanceId: 0, _eventGroupId: eventGroupId, _buyerProfilId: buyerProfilId  });
    }

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'GetContentPage',
        data: sData,
        async: false,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        success: function (response) {

            if (response.d.split(':')[0] == "danger") {
                ShowError("error", ReadXmlTranslate(response.d.split('|')[0]), "alert alert-danger alert-dismissable", 3000);
            } else {
                $('#loading').html('');
                $("#content").html("");
                $("#content").html(response.d);

                $("#EnregistrerHaut").on('click', function () {
                    SaveContent(pageName, "haut", "");
                });

                $("#EnregistrerBas").on('click', function () {
                    var text = $("#wysieditor_abo_formule2 div").html();
                    SaveContent(pageName, "bas", "");
                });

                $("#GetLastFileHaut").on('click', function () {
                    ConfirmMessage(pageName, "haut", "");
                    // GetLastFile(pageName, "haut", "");
                });

                $("#GetLastFileBas").on('click', function () {
                    ConfirmMessage(pageName, "haut", "");
                    //GetLastFile(pageName, "haut", "");
                });

                $("#wysieditor_abo_formule1").on("change keyup paste", function () {
                    isWriteTextHaut = true;
                });


                $("#wysieditor_abo_formule2").on("change keyup paste", function () {
                    isWriteTextBas = true;
                });

            }


            LaunchTraduction();

        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message, "alert alert-danger alert-dismissable");
            console.log(XMLHttpRequest.responseJSON.Message);
            $('#loading').html('');
        }
    });

    }

//direction = haut ou bas
function GetLastFile(pageName, direction, platformName) {

    console.log("GetLastFile " + pageName + " , " + direction);
    var platformName = $("#myTab li.active a").data('key');

    var text = "";

    // Récupérer les mêmes paramètres que pour SaveContent
    var buyerProfilId = 0;
    if($('#selectBuyerProfil').length > 0){
       if ($('#selectBuyerProfil option:selected').val() != undefined)
           buyerProfilId = $('#selectBuyerProfil').val();
    }

    var arrSeances = GetSeances();
    var thisManifGroupId = $('#select_manifs_groups option:selected').val();
    var thisManifId = $('#select_manifs option:selected').val();

    var sData = JSON.stringify({ _pageName: pageName, _direction: direction, _ddlLang: ddlLang, _appKey: platformName, _manifId: thisManifId, _arrSeancesId: arrSeances, _eventGroupId: thisManifGroupId, _buyerProfilId: buyerProfilId });

    $.ajax({
        type: "POST",
        url: sAjaxSourceUrl + 'GetLastFile',
        data: sData,
        async: false,
        contentType: 'application/json; charset=utf-8',
        dataType: "json",
        beforeSend: function () {
            var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
            $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
        },
        success: function (response) {
            $('#loading').html('');


            if (response.d.split(':')[0] == "success") {

                if (direction == "haut") {
                    $("#wysieditor_abo_formule1").html(response.d);
                } else {
                    $("#wysieditor_abo_formule2").html(response.d);
                }
                
                ShowError("error", ReadXmlTranslate('success_commentaire_insert'), "alert alert-" + response.d.split(':')[0] + " alert-dismissable");
            } else {
                ShowError("error", ReadXmlTranslate(response.d.split(':')[1]), "alert alert-" + response.d.split(':')[0] + " alert-dismissable");
            }


        },
        error: function (XMLHttpRequest, textStatus, errorThrown) {
            ShowError("error", XMLHttpRequest.responseJSON.Message, "alert alert-danger alert-dismissable");
            console.log(XMLHttpRequest.responseJSON.Message);
            $('#loading').html('');
        }
    });

}

//direction = haut ou bas
function SaveContent(pageName, direction, pageNameCurrent) {

    isWriteTextHaut = false;
    isWriteTextBas = false;
    var platformName = $("#myTab li.active a").data('key');

    if (pageNameCurrent != "") {
       pageName = $("#myTab li.active a").data('id');

    }

     var buyerProfilId = 0;
     if($('#selectBuyerProfil').length > 0){
        if ($('#selectBuyerProfil option:selected').val() != undefined)
            buyerProfilId = $('#selectBuyerProfil').val();
    
     }
    


    var text = "";
    if (direction == "haut") {
        text = $("#wysieditor_abo_formule1").html();
    } else {
        text = $("#wysieditor_abo_formule2").html();
    }

    var valid_links_regex = "MsoNormal";

    var re = new RegExp(valid_links_regex, 'ig')

  //  if (text.match(re)) {
        ShowError("error", $('#lblErrorCopyMotsWord').text(), "alert alert-danger alert-dismissable");

   // } else {

      //  var sData = JSON.stringify({ _pageName: pageName, _text: text, _direction: direction, _ddlLang: ddlLang, _appKey: $('.tabbable').data('key') });

        var arrSeances = GetSeances();
        var thisManifGroupId = $('#select_manifs_groups option:selected').val();
        var thisManifId = $('#select_manifs option:selected').val();
    var sData = JSON.stringify({ _pageName: pageName, _text: text, _direction: direction, _ddlLang: ddlLang, _appKey: platformName, _manifId: thisManifId, _arrSeancesId: arrSeances, _eventGroupId: thisManifGroupId, _buyerProfilId: buyerProfilId });
      

        $.ajax({
            type: "POST",
            url: sAjaxSourceUrl + 'SaveContent',
            data: sData,
            async: false,
            contentType: 'application/json; charset=utf-8',
            dataType: "json",
            beforeSend: function () {
                var html = ' <img src="../assets/images/loader.gif" alt="loader" />';
                $('#loading').html(html + $('#lblLoadingChargementEnCours').text());
            },
            success: function (response) {
                $('#loading').html('');

                if (response.d.split(':')[0] == "success") {
                    ShowError("error", ReadXmlTranslate('success_commentaire_insert'), "alert alert-" + response.d.split(':')[0] + " alert-dismissable");
                } else {
                    //mettre une belle popup pour afficher que c'est sauvegarder
                    ShowError("error", ReadXmlTranslate(response.d.split(':')[0]).replace('[filename]', response.d.split(':')[1]), "alert alert-danger alert-dismissable");
                }

                
            },
            error: function (XMLHttpRequest, textStatus, errorThrown) {
                ShowError("error", XMLHttpRequest.responseJSON.Message, "alert alert-danger alert-dismissable");
                console.log(XMLHttpRequest.responseJSON.Message);
                $('#loading').html('');
            }
        });

   // }

}




function fnOpenNormalDialog() {
    $("#dialog-confirm").html("Confirm Dialog Box");

    // Define the Dialog and its properties.
    $("#dialog-confirm").dialog({
        resizable: false,
        modal: true,
        title: "Modal",
        height: 250,
        width: 400,
        buttons: {
            "Yes": function () {
                $(this).dialog('close');
                callback(true);
            },
            "No": function () {
                $(this).dialog('close');
                callback(false);
            }
        }
    });
}


