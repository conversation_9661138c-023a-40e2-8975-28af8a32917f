﻿using login.classes;
using login.wsThemisAdmin2010;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Web;
using System.Web.Script.Services;
using System.Web.Services;
using System.Web.UI.WebControls;

namespace login.pages_visuels
{
    public partial class commentaires_pages : basePage
    {
        private static readonly log4net.ILog log = log4net.LogManager.GetLogger(System.Reflection.MethodBase.GetCurrentMethod().DeclaringType);

        public string TitleWidgetMode { get; set; } = "title_page_not_widget_mode";
        public string FilesWidgetMode { get; set; } = "";

        
        public List<ProfilAcheteurEntity> BuyerProfils { get; set; } = new List<ProfilAcheteurEntity>();
        protected void Page_Load(object sender, EventArgs e)
        {

            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                string strStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                string widgetModePagesAvalaible = MyConfigurationManager.AppSettings("WidgetModePagesAvailable");
                log.DebugFormat("Get WidgetModePagesAvailable web.config: {0} : ", widgetModePagesAvalaible);
               
                string filesInfosDirectoryPath = MyConfigurationManager.AppSettings("FilesCommentaireIndiv").Replace("[idstructure]", strStructureId);
                log.DebugFormat("Get filesInfosDirectory web.config: {0} : ", filesInfosDirectoryPath);

                var filesToInclude = widgetModePagesAvalaible.ToLower().Split(',');
                var filesSearched = Directory.GetFiles(Path.GetDirectoryName(filesInfosDirectoryPath), "info*.*", SearchOption.TopDirectoryOnly).Where(f => filesToInclude.Any(f.ToLower().Contains)).ToList();

                log.DebugFormat($"nb filesSearched {filesSearched.Count()}");

                dynamic indivSettingsJson = GestionAppsettingsJson.LoadAppSettingsJson(int.Parse(strStructureId), 0, "INDIV", "physicalPathOfSettingsJSON", false);

                if (indivSettingsJson != null)
                {
                    log.DebugFormat($"indivSettingsJson != null");

                    if (IsWidgetModeSettings(indivSettingsJson))
                    {
                        TitleWidgetMode = "title_page_widget_mode";
                        log.DebugFormat("title_page_widget_mode");
                        //si l'option est activé et au moins 1 fichier sans widget
                        if (filesSearched.Any(f => !f.ToLower().Contains("widget")))
                        {
                            log.DebugFormat("files_page_not_widget_mode");

                            FilesWidgetMode = "files_page_not_widget_mode";
                        }
                    }
                    else
                    {
                        TitleWidgetMode = "title_page_not_widget_mode";
                    }
                }

                wsThemisAdmin2010.WSAdminClient wsAdmin = new wsThemisAdmin2010.WSAdminClient();
                BuyerProfils = wsAdmin.GetListProfilsAcheteurs(strStructureId).ToList();
            }
        }


        /// <summary>
        /// retourne la liste des manifestations
        /// </summary>
        /// <param name="_pageName">le nom du fichier a récupérer </param>
        /// <param name="_direction">haut ou bas</param>
        /// <returns></returns>
        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static List<OSessionEntity> LoadListSeances(int _manifId, string _appKey)
        {
            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                string strStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Debug("LoadListSeances dans commentaire - structure : " + strStructureId);

                wsThemisAdmin2010.WSAdminClient wsAdmin = new wsThemisAdmin2010.WSAdminClient();

                try
                {

                    string userLanguage = MyConfigurationManager.GetUserLanguage();
                    log.Debug("LoadListSeances - userLanguage : " + userLanguage);

                    List<int> listGroupe = new List<int>();
                    int OffreId = 0;

                    if (System.Web.HttpContext.Current.Session["mygroupeDeManifs"] != null && System.Web.HttpContext.Current.Session["mygroupeDeManifs"].ToString() != "all")
                    {

                        List<string> lstGrpManifs = System.Web.HttpContext.Current.Session["mygroupeDeManifs"].ToString().Split(',').ToList();

                        foreach (string item in lstGrpManifs)
                        {
                            listGroupe.Add(int.Parse(item));
                        }
                    }

                    log.DebugFormat("Appel du Webservice GetListeManifs avec structure : {0} ", strStructureId);
                    List<OSessionEntity> lstSeances = wsAdmin.GetSeancesList(strStructureId, OffreId, listGroupe.ToArray(), userLanguage, 0, _manifId).ToList();
                    // lstSeances = wsAdmin.GetSeancesList(sStructureID, OffreId, listGroupe.ToArray(), userLanguage, 0, eventId).ToList();

                    return lstSeances;
                }
                catch (Exception ex)
                {
                    log.Error("catch LoadListEventsSessions du web service " + ex.Message);
                    throw new Exception(ex.Message);

                }
                finally
                {
                    if (wsAdmin.State == System.ServiceModel.CommunicationState.Opened)
                    {
                        wsAdmin.Close();
                    }
                }
            }
            return null;
        }




        /// <summary>
        /// Moulinette permettant de renomer les fichiers commentaire pour le mode widget
        /// </summary>
        /// <param name="convertToWidgetMode">booléen permettant de savois si on convertit ou pas les pages en mode widget</param>
        /// <returns></returns>
        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string ConvertToWidgetMode(bool convertToWidgetMode)
        {
            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                string strStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Debug("ConvertToWidgetMode dans commentaire - structure : " + strStructureId);


                try
                {
                    //on prends que les pages qu 
                    //List<string> lstPagesToTake = new List<string>() { "infofChoixSeance", "infofChoixSeanceOnMultiSeance", "infofPanierS" };

                    string widgetModePagesAvalaible = MyConfigurationManager.AppSettings("WidgetModePagesAvailable");
                    log.DebugFormat("Get WidgetModePagesAvailable web.config: {0} : ", widgetModePagesAvalaible);


                    //Dossiers FILESINFOS dans le quel se trouve tous les fichiers qui ont un commentaire
                    string filesInfosDirectoryPath = MyConfigurationManager.AppSettings("FilesCommentaireIndiv").Replace("[idstructure]", strStructureId);
                    log.DebugFormat("Get filesInfosDirectory web.config: {0} : ", filesInfosDirectoryPath);


                    //string[] commentsFilesLists = Directory.GetFiles(Path.GetDirectoryName(filesInfosDirectoryPath));

                    //var commentsFilesList = Directory.GetFiles(Path.GetDirectoryName(filesInfosDirectoryPath), "info.*", SearchOption.TopDirectoryOnly).Where(s => s.ToLower().Contains("info")).ToList();
                    var commentsFilesList = Directory.GetFiles(Path.GetDirectoryName(filesInfosDirectoryPath), "info*.*", SearchOption.TopDirectoryOnly).ToList();

                    //On renomme les fichiers avec "Widget" avant haut ou bas
                    if (convertToWidgetMode)
                    {
                        foreach (string commentFile in commentsFilesList)
                        {
                            //var thisFileWithoutExtension = Path.GetFileNameWithoutExtension(Path.GetFileName(commentFile));
                            string thisFileWithoutExtension = Path.GetFileName(commentFile);
                            FileInfo file = new FileInfo(commentFile);

                            if (!commentFile.ToLower().Contains("widget"))
                            {
                                int indexDirectionHaut = thisFileWithoutExtension.IndexOf("haut");
                                int indexDirectionBas = thisFileWithoutExtension.IndexOf("bas");
                                int indexDirectionHead = thisFileWithoutExtension.IndexOf("head");

                                string tmpFileName = "";

                                //on enlève le surplaus du nom du fichier afin de le comparer avec contains
                                if (indexDirectionHaut > 0)
                                {
                                    tmpFileName = thisFileWithoutExtension.Remove(indexDirectionHaut).Replace("info", "");
                                }

                                if (indexDirectionBas > 0)
                                {
                                    tmpFileName = thisFileWithoutExtension.Remove(indexDirectionBas).Replace("info", "");
                                }

                                if (indexDirectionHead > 0)
                                {
                                    tmpFileName = thisFileWithoutExtension.Remove(indexDirectionHead).Replace("info", "");
                                }

                                if (!string.IsNullOrEmpty(tmpFileName) && widgetModePagesAvalaible.Split(',').Contains(tmpFileName, StringComparer.OrdinalIgnoreCase))
                                {
                                    if (indexDirectionHaut > 0)
                                    {
                                        string newName = thisFileWithoutExtension.Insert(indexDirectionHaut, "widget");


                                        if (File.Exists(Path.Combine(Path.GetDirectoryName(commentFile), newName)))
                                        {
                                            string existFile = (Path.Combine(Path.GetDirectoryName(commentFile), newName));
                                            FileInfo fi = new FileInfo(existFile);
                                            fi.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), "old_" + fi.Name));
                                        }
                                        file.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), newName));
                                    }

                                    if (indexDirectionBas > 0)
                                    {
                                        string newName = thisFileWithoutExtension.Insert(indexDirectionBas, "widget");

                                        if (File.Exists(Path.Combine(Path.GetDirectoryName(commentFile), newName)))
                                        {
                                            string existFile = (Path.Combine(Path.GetDirectoryName(commentFile), newName));
                                            FileInfo fi = new FileInfo(existFile);
                                            fi.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), "old_" + fi.Name));
                                        }
                                        file.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), newName));
                                    }



                                    if (indexDirectionHead > 0)
                                    {
                                        string newName = thisFileWithoutExtension.Insert(indexDirectionHead, "widget");

                                        if (File.Exists(Path.Combine(Path.GetDirectoryName(commentFile), newName)))
                                        {
                                            string existFile = (Path.Combine(Path.GetDirectoryName(commentFile), newName));
                                            FileInfo fi = new FileInfo(existFile);
                                            fi.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), "old_" + fi.Name));
                                        }
                                        file.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), newName));
                                    }
                                }
                            }
                        }

                    }
                    else
                    {
                        foreach (string commentFile in commentsFilesList)
                        {
                            //var thisFileWithoutExtension = Path.GetFileNameWithoutExtension(Path.GetFileName(commentFile));
                            string thisFileWithoutExtension = Path.GetFileName(commentFile);
                            FileInfo file = new FileInfo(commentFile);


                            if (commentFile.ToLower().Contains("widget"))
                            {

                                int indexDirectionHaut = thisFileWithoutExtension.IndexOf("haut");
                                int indexDirectionBas = thisFileWithoutExtension.IndexOf("bas");

                                string tmpFileName = "";

                                //on enlève le surplaus du nom du fichier afin de le comparer avec contains
                                if (indexDirectionHaut > 0)
                                {
                                    tmpFileName = thisFileWithoutExtension.Remove(indexDirectionHaut).Replace("info", "").Replace("widget", string.Empty).Replace("Widget", string.Empty);
                                }

                                if (indexDirectionBas > 0)
                                {
                                    tmpFileName = thisFileWithoutExtension.Remove(indexDirectionBas).Replace("info", "").Replace("widget", string.Empty).Replace("Widget", string.Empty);
                                }

                                if (!string.IsNullOrEmpty(tmpFileName) && widgetModePagesAvalaible.Split(',').Contains(tmpFileName, StringComparer.OrdinalIgnoreCase))
                                {
                                    int indexWidget = thisFileWithoutExtension.ToLower().IndexOf("widget");
                                    if (indexDirectionHaut > 0)
                                    {
                                        string newName = thisFileWithoutExtension.Remove(indexWidget, 6);

                                        if (File.Exists(Path.Combine(Path.GetDirectoryName(commentFile), newName)))
                                        {
                                            string existFile = (Path.Combine(Path.GetDirectoryName(commentFile), newName));
                                            FileInfo fi = new FileInfo(existFile);
                                            fi.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), "old_" + fi.Name));
                                        }
                                        file.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), newName));
                                    }

                                    if (indexDirectionBas > 0)
                                    {
                                        string newName = thisFileWithoutExtension.Remove(indexWidget, 6);

                                        if(File.Exists(Path.Combine(Path.GetDirectoryName(commentFile), newName)))
                                        {
                                            string existFile = (Path.Combine(Path.GetDirectoryName(commentFile), newName));
                                            FileInfo fi = new FileInfo(existFile);
                                            fi.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), "old_" + fi.Name) );
                                            //file.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), "old_"+ newName));
                                        }
                                        file.MoveTo(Path.Combine(Path.GetDirectoryName(commentFile), newName));

                                    }
                                }
                            }
                        }
                    }
                    return "success:msg_success_rename_widget_file";
                }
                catch (Exception ex)
                {
                    log.Error("catch LoadListManifestationsGroups du web service " + ex.Message);
                    throw new Exception(ex.Message);
                }

            }

            log.Error("Aucune structure sélectionnée");
            return "danger:aucune_structure_selectionnee";
        }

        /// <summary>
        /// retourne la liste des groupes de manifestations
        /// </summary>
        /// <returns></returns>
        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static List<EventsGroupsEntity1> LoadListManifestationsGroups()
        {
            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                string strStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Debug("LoadListManifestationsGroups dans commentaire - structure : " + strStructureId);

                wsThemisAdmin2010.WSAdminClient wsAdmin = new wsThemisAdmin2010.WSAdminClient();

                try
                {
                    List<int> listGroupe = new List<int>();
                    listGroupe = GetGroupeListManifs();

                    log.DebugFormat("Appel du Webservice LoadListManifestationsGroups avec structure : {0} ", strStructureId);
                    List<EventsGroupsEntity1> lstEventsGroups = wsAdmin.GetListEventsGroups(strStructureId, true).ToList();
                    lstEventsGroups.Any(eg => listGroupe.Any(g => g == eg.eventGroupID));


                    return lstEventsGroups;
                }
                catch (Exception ex)
                {
                    log.Error("catch LoadListManifestationsGroups du web service " + ex.Message);
                    throw new Exception(ex.Message);

                }
                finally
                {
                    if (wsAdmin.State == System.ServiceModel.CommunicationState.Opened)
                    {
                        wsAdmin.Close();
                    }
                }
            }
            return null;
        }

        /// <summary>
        /// retourne la liste des manifestations
        /// </summary>
        /// <param name="_pageName">le nom du fichier a récupérer </param>
        /// <param name="_direction">haut ou bas</param>
        /// <returns></returns>
        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static List<OEventEntity> LoadListManifestations(int _eventGroupIdSelected)
        {
            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                string strStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Debug("LoadListManifestations dans commentaire - structure : " + strStructureId);

                wsThemisAdmin2010.WSAdminClient wsAdmin = new wsThemisAdmin2010.WSAdminClient();

                try
                {

                    string userLanguage = MyConfigurationManager.GetUserLanguage();
                    log.Debug("LoadListManifestations - userLanguage : " + userLanguage);

                    List<int> listGroupe = new List<int>();
                    int OffreId = 0;

                    if (_eventGroupIdSelected > 0)
                    {
                        listGroupe.Add(_eventGroupIdSelected);
                    }
                    // listGroupe = GetGroupeListManifs();


                    log.DebugFormat("Appel du Webservice GetListeManifs avec structure : {0} ", strStructureId);
                    List<OEventEntity> lstEvents = wsAdmin.GetListeManifs(strStructureId, OffreId, listGroupe.ToArray(), userLanguage, 0).ToList();

                    return lstEvents;
                }
                catch (Exception ex)
                {
                    log.Error("catch LoadListEventsSessions du web service " + ex.Message);
                    throw new Exception(ex.Message);

                }
                finally
                {
                    if (wsAdmin.State == System.ServiceModel.CommunicationState.Opened)
                    {
                        wsAdmin.Close();
                    }
                }
            }
            return null;
        }

        private static List<int> GetGroupeListManifs()
        {

            if (HttpContext.Current.Session["mygroupeDeManifs"] != null && System.Web.HttpContext.Current.Session["mygroupeDeManifs"].ToString() != "all")
            {
                List<int> listGroupe = HttpContext.Current.Session["mygroupeDeManifs"].ToString().Split(',').Select(int.Parse).ToList();

                return listGroupe;
                /* List<string> lstGrpManifs = System.Web.HttpContext.Current.Session["mygroupeDeManifs"].ToString().Split(',').ToList();

                foreach (string item in lstGrpManifs)
                {
                    listGroupe.Add(int.Parse(item));
                }*/
            }
            return new List<int>();
        }
        /// <summary>
        /// retourne le contenu du dernier fichier sauvegarder
        /// </summary>
        /// <param name="_pageName">le nom du fichier a récupérer </param>
        /// <param name="_direction">haut ou bas</param>
        /// <returns></returns>
        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static List<LanguagesEntity> LoadListLanguages()
        {


            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                string strStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Debug("LoadListLanguages dans commentaire - structure : " + strStructureId);

                wsThemisAdmin2010.WSAdminClient wsClt = new wsThemisAdmin2010.WSAdminClient();

                try
                {
                    log.DebugFormat("Appel du Webservice LoadListEventsSessions avec structure : {0} ", strStructureId);
                    List<LanguagesEntity> lstLangues = wsClt.LoadLanguages(strStructureId).ToList();

                    return lstLangues;
                }
                catch (Exception ex)
                {
                    log.Error("catch LoadListEventsSessions du web service " + ex.Message);
                    throw new Exception(ex.Message);

                }
                finally
                {
                    if (wsClt.State == System.ServiceModel.CommunicationState.Opened)
                    {
                        wsClt.Close();
                    }
                }
            }
            return null;
        }


        /// <summary>
        /// retourne le contenu du dernier fichier sauvegarder
        /// </summary>
        /// <param name="_pageName">le nom du fichier a récupérer </param>
        /// <param name="_direction">haut ou bas</param>
        /// <returns></returns>
        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string GetLastFile(string _pageName, string _direction, string _ddlLang, string _appKey, int _manifId, List<OSessionEntity> _arrSeancesId, int _eventGroupId, int _buyerProfilId)
        {
            MyConfigurationManager.SetUserLanguage();

            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                string strStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Debug("GetLastFile dans commentaire - structure : " + strStructureId);


                string _siteName = _pageName + _direction;

                // Construire le nom de fichier de la même manière que dans SaveContent
                //Si le paramètrage pour les widgets est activé
                dynamic indivSettingsJson = GestionAppsettingsJson.LoadAppSettingsJson(int.Parse(strStructureId), 0, "INDIV", "physicalPathOfSettingsJSON", false);

                if (_arrSeancesId.Count > 0)
                {
                    // Pour les séances, on prend le premier élément pour construire le nom
                    OSessionEntity firstSession = _arrSeancesId[0];
                    string fileNameToLoad = _pageName + _direction + "." + _manifId + "." + firstSession.sessionId;

                    if (_buyerProfilId > 0 && _eventGroupId > 0)
                    {
                        fileNameToLoad = _pageName + _direction + "." + "grp" + _eventGroupId + "." + _manifId + "." + firstSession.sessionId + ".pa" + _buyerProfilId;
                    }
                    else if (_buyerProfilId > 0)
                    {
                        fileNameToLoad = _pageName + _direction + ".pa" + _buyerProfilId;
                    }
                    else if (_eventGroupId > 0)
                    {
                        fileNameToLoad = _pageName + _direction + "." + "grp" + _eventGroupId + "." + _manifId + "." + firstSession.sessionId;
                    }

                    if (IsWidgetModeSettings(indivSettingsJson) && (indivSettingsJson != null && indivSettingsJson.sessionsList.isWidget.Value && _pageName.Contains("fchoixseance")))
                    {
                        if (_buyerProfilId > 0 && _eventGroupId > 0)
                        {
                            fileNameToLoad = "infofChoixSeanceWidget" + _direction + "." + "grp" + _eventGroupId + "." + _manifId + "." + firstSession.sessionId + ".pa" + _buyerProfilId;
                        }
                        else if (_buyerProfilId > 0)
                        {
                            fileNameToLoad = "infofChoixSeanceWidget" + _direction + ".pa" + _buyerProfilId;
                        }
                        else if (_eventGroupId > 0 && _manifId > 0)
                        {
                            fileNameToLoad = "infofChoixSeanceWidget" + _direction + ".grp" + _eventGroupId + "." + _manifId + "." + firstSession.sessionId;
                        }
                        else
                        {
                            fileNameToLoad = "infofChoixSeanceWidget" + _direction + ".grp" + _eventGroupId + "." + _manifId + "." + firstSession.sessionId;
                        }
                    }

                    _siteName = fileNameToLoad;
                }
                else if (_eventGroupId > 0)
                {
                    string fileNameToLoad = _pageName + _direction + "." + "grp" + _eventGroupId;

                    if (IsWidgetModeSettings(indivSettingsJson) && (indivSettingsJson != null && indivSettingsJson.sessionsList.isWidget.Value && _pageName.Contains("fchoixseance")))
                    {
                        if (_buyerProfilId > 0 && _eventGroupId > 0 && _manifId > 0)
                        {
                            fileNameToLoad = "infofChoixSeanceWidget" + _direction + ".grp" + _eventGroupId + "." + _manifId + ".pa" + _buyerProfilId;
                        }
                        else if (_buyerProfilId > 0 && _eventGroupId > 0)
                        {
                            fileNameToLoad = "infofChoixSeanceWidget.grp" + _eventGroupId + ".pa" + _buyerProfilId;
                        }
                        else if (_buyerProfilId > 0)
                        {
                            fileNameToLoad = "infofChoixSeanceWidget.pa" + _buyerProfilId;
                        }
                        else if (_eventGroupId > 0 && _manifId > 0)
                        {
                            fileNameToLoad = "infofChoixSeanceWidget" + _direction + ".grp" + _eventGroupId + "." + _manifId;
                        }
                        else
                        {
                            fileNameToLoad = "infofChoixSeanceWidget" + _direction + ".grp" + _eventGroupId;
                        }
                    }

                    _siteName = fileNameToLoad;
                }

                log.Debug("_siteName : " + _siteName);


                //récupère le chemin physique su site vers INDIV
                //string physiquepath = GestionCommentaires.GetConfigurationKey("Indiv");
                //log.Debug("physiquepath " + physiquepath);
                string physiquepath = MyConfigurationManager.AppSettings("FilesCommentaire" + _appKey);
                log.DebugFormat("Get PhysicalPathOfTheSite: {0} : ", physiquepath);


                string langue = "";
                if (string.IsNullOrEmpty(_ddlLang))
                {
                    langue = MyConfigurationManager.GetUserLanguage();
                }
                else
                {
                    langue = _ddlLang;
                }

                log.DebugFormat("Get language: {0} : ", langue);


                string pathTagsSVG = GestionCommentaires.GetPathFile("FilesCommentaire", strStructureId, _siteName, physiquepath, langue, true);
                log.Debug("pathTagsSVG : " + pathTagsSVG);

                // si le chemin avec structure existe
                if (pathTagsSVG.Split('|')[0] != "error")
                {
                    string lastFile = GestionFichiers.GetPathLastFile(pathTagsSVG, _siteName);


                    if (lastFile.Split('|')[0] != "ok")
                    {
                        return "danger:" + lastFile.Split('|')[1];
                    }
                    else
                    {
                        try
                        {
                            string content = GestionFichiers.ReadFile(lastFile.Split('|')[1]);
                            return content;

                        }
                        catch (Exception ex)
                        {
                            log.Error("catch GetLastFileTags siteName : " + _pageName + " message : " + ex.Message);
                            throw new Exception(ex.Message);
                        }
                    }

                }
                else
                {
                    log.Debug("Le chemin n'a pas été trouvé : " + pathTagsSVG);
                    return "svg_file_not_found : " + pathTagsSVG;
                }

            }
            return "danger:aucune_structure_selectionnee";
        }



        /// <summary>
        /// Enregistre le texte dans un nouveau fichier en sauvegardant l'ancien 
        /// </summary>
        /// <param name="_pageName">nom du fichier</param>
        /// <param name="_text">texte à enregistrer</param>
        /// <param name="_direction">haut ou bas</param>
        /// <returns></returns>
        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string SaveContent(string _pageName, string _text, string _direction, string _ddlLang, string _appKey, int _manifId, List<OSessionEntity> _arrSeancesId, int _eventGroupId, int _buyerProfilId)
        {
            MyConfigurationManager.SetUserLanguage();

            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                wsThemisAdmin2010.WSAdminClient wsAdmin = new wsThemisAdmin2010.WSAdminClient();

                string strStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Debug("SaveContent dans commentaire - structure : " + strStructureId);

                //récupère le chemin physique su site vers indiv
                //string physiquepath = GestionTemplate.GetConfigurationKey("indiv");
                //log.DebugFormat("Get PhysicalPathOfTheSite: {0} : ", physiquepath);
                string physiquepath = MyConfigurationManager.AppSettings("FilesCommentaire" + _appKey);
                log.DebugFormat("Get PhysicalPathOfTheSite: {0} : ", physiquepath);


                string langue = "";
                if (string.IsNullOrEmpty(_ddlLang))
                {
                    langue = MyConfigurationManager.GetUserLanguage();
                }
                else
                {
                    langue = _ddlLang;
                }

                log.DebugFormat("Get language: {0} : ", langue);



                //Si le paramètrage pour les widgets est activé               
                dynamic indivSettingsJson = GestionAppsettingsJson.LoadAppSettingsJson(int.Parse(strStructureId), 0, "INDIV", "physicalPathOfSettingsJSON", false);


                if (_arrSeancesId.Count > 0)
                {
                    foreach (OSessionEntity item in _arrSeancesId)
                    {
                        string fileNameToSave = _pageName + _direction + "." + _manifId + "." + item.sessionId;


                        if (_buyerProfilId > 0 && _eventGroupId > 0)
                        {
                            fileNameToSave = _pageName + _direction + "." + "grp" + _eventGroupId + "." + _manifId + "." + item.sessionId+ ".pa"+_buyerProfilId;
                        }else if(_buyerProfilId > 0)
                        {
                            fileNameToSave = _pageName + _direction + ".pa" + _buyerProfilId;
                        }
                        else if (_eventGroupId > 0)
                        {
                            fileNameToSave = _pageName + _direction + "." + "grp" + _eventGroupId + "." + _manifId + "." + item.sessionId;
                        }

                        string pathTags = GestionCommentaires.GetPathFile("FilesCommentaire", strStructureId, fileNameToSave, physiquepath, langue);


                        bool isModeCalendar = wsAdmin.IsModeCalendarEvent(strStructureId, _manifId);

                        if (isModeCalendar && _pageName.Contains("fchoixseance"))
                        {
                            if (_buyerProfilId > 0 && _eventGroupId > 0)
                            {
                                fileNameToSave = "infofChoixSeanceOnMultiSeance" + _direction + "." + "grp" + _eventGroupId + "." + _manifId + "." + item.sessionId+".pa"+_buyerProfilId;
                            }
                            else if (_buyerProfilId > 0)
                            {
                                fileNameToSave = "infofChoixSeanceOnMultiSeance" + _direction + ".pa" + _buyerProfilId;
                            }
                            else if (_eventGroupId > 0)
                            {
                                fileNameToSave = "infofChoixSeanceOnMultiSeance" + _direction + "." + "grp" + _eventGroupId + "." + _manifId + "." + item.sessionId;
                            }

                            fileNameToSave = "infofChoixSeanceOnMultiSeance" + _direction + "." + _manifId + "." + item.sessionId;
                        }

                        if (IsWidgetModeSettings(indivSettingsJson) && (indivSettingsJson != null && indivSettingsJson.sessionsList.isWidget.Value))
                        {
                            fileNameToSave = "infofChoixSeanceWidget" + _direction + "." + "grp" + _eventGroupId + "." + _manifId + "." + item.sessionId;
                        }



                        //Si le fichier commentaire avec la structure existe, on le déplace dans un dossier nommé svg
                        if (File.Exists(pathTags))
                        {
                            // prends le fichier et le deplace dans le dossier SVG en renommant le fichier avec un datetime
                            GestionFichiers.MoveFile(pathTags, "\\svg");
                        }
                        else if (pathTags.Split('|')[0] == "filenotfound")
                        {

                            //si le fichier n'existe pas, on construit le chemin pour l'enregistrer
                            pathTags = physiquepath.Replace("[idstructure]", strStructureId).Replace("[siteName]", fileNameToSave).Replace("[.lang]", "." + langue);
                        }

                        GestionFichiers.SaveFile(pathTags, _text);
                    }
                }
                else if (_manifId > 0 && _arrSeancesId.Count == 0)
                {
                    bool isModeCalendar = wsAdmin.IsModeCalendarEvent(strStructureId, _manifId);

                    string fileNameToSave = _pageName + _direction + "." + _manifId;

                    if(_buyerProfilId > 0 && _eventGroupId > 0)
                    {
                        fileNameToSave = _pageName + _direction +  ".grp" + _eventGroupId + "." + _manifId + ".pa" + _buyerProfilId;
                    }
                    else if(_buyerProfilId > 0)
                    {
                        fileNameToSave = _pageName + _direction +  ".pa" + _buyerProfilId;
                    }
                    else if (_eventGroupId > 0)
                    {
                        fileNameToSave = _pageName + _direction + ".grp" + _eventGroupId + "." + _manifId;
                    }

                    if (isModeCalendar && _pageName.Contains("fchoixseance"))
                    {
                        fileNameToSave = "infofChoixSeanceOnMultiSeance" + _direction + "." + _manifId;



                        if (_buyerProfilId > 0 && _eventGroupId > 0 && _manifId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceOnMultiSeance" + _direction + "." + "grp" + _eventGroupId + "." + _manifId+".pa"+_buyerProfilId;
                        }
                        else if (_buyerProfilId > 0 && _eventGroupId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceOnMultiSeance.grp" + _eventGroupId + ".pa"+_buyerProfilId;
                        }
                        else if (_buyerProfilId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceOnMultiSeance.pa" + _buyerProfilId;
                        }
                        else if(_eventGroupId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceOnMultiSeance" + _direction + "." + "grp" + _eventGroupId + "." + _manifId;
                        }
                    }

                    if (IsWidgetModeSettings(indivSettingsJson) &&  (indivSettingsJson != null && indivSettingsJson.sessionsList.isWidget.Value))
                    {
                        if (_buyerProfilId > 0 && _eventGroupId > 0 && _manifId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceWidget" + _direction + "." + "grp" + _eventGroupId + "." + _manifId + ".pa" + _buyerProfilId;
                        }
                        else if (_buyerProfilId > 0 && _eventGroupId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceWidget.grp" + _eventGroupId + ".pa" + _buyerProfilId;
                        }
                        else if (_buyerProfilId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceWidget.pa" + _buyerProfilId;
                        }
                        else if (_eventGroupId > 0 && _manifId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceWidget" + _direction +  ".grp" + _eventGroupId + "." + _manifId;
                        }
                        else
                        {
                            fileNameToSave = "infofChoixSeanceWidget" + _direction + ".grp" + _eventGroupId + "." + _manifId;
                        }


                    }

                    string pathTags = GestionCommentaires.GetPathFile("FilesCommentaire", strStructureId, fileNameToSave, physiquepath, langue);


                    //Si le fichier commentaire avec la structure existe, on le déplace dans un dossier nommé svg
                    if (File.Exists(pathTags))
                    {
                        // prends le fichier et le deplace dans le dossier SVG en renommant le fichier avec un datetime
                        GestionFichiers.MoveFile(pathTags, "\\svg");
                    }
                    else if (pathTags.Split('|')[0] == "filenotfound")
                    {

                        //si le fichier n'existe pas, on construit le chemin pour l'enregistrer
                        pathTags = physiquepath.Replace("[idstructure]", strStructureId).Replace("[siteName]", fileNameToSave).Replace("[.lang]", "." + langue);
                    }

                    GestionFichiers.SaveFile(pathTags, _text);
                }
                else if (_eventGroupId > 0)
                {

                    string fileNameToSave = _pageName + _direction + "." + "grp" + _eventGroupId;

                    if (IsWidgetModeSettings(indivSettingsJson) &&  (indivSettingsJson != null && indivSettingsJson.sessionsList.isWidget.Value && _pageName.Contains("fchoixseance")))
                    {
                        if (_buyerProfilId > 0 && _eventGroupId > 0 && _manifId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceWidget" + _direction +  ".grp" + _eventGroupId + "." + _manifId + ".pa" + _buyerProfilId;
                        }
                        else if (_buyerProfilId > 0 && _eventGroupId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceWidget.grp" + _eventGroupId + ".pa" + _buyerProfilId;
                        }
                        else if (_buyerProfilId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceWidget.pa" + _buyerProfilId;
                        }
                        else if (_eventGroupId > 0 && _manifId > 0)
                        {
                            fileNameToSave = "infofChoixSeanceWidget" + _direction + ".grp" + _eventGroupId + "." + _manifId;
                        }
                        else
                        {
                            fileNameToSave = "infofChoixSeanceWidget" + _direction +  ".grp" + _eventGroupId;
                        }
                    }


                    string pathTags = GestionCommentaires.GetPathFile("FilesCommentaire", strStructureId, fileNameToSave, physiquepath, langue);


                    //Si le fichier commentaire avec la structure existe, on le déplace dans un dossier nommé svg
                    if (File.Exists(pathTags))
                    {
                        // prends le fichier et le deplace dans le dossier SVG en renommant le fichier avec un datetime
                        GestionFichiers.MoveFile(pathTags, "\\svg");
                    }
                    else if (pathTags.Split('|')[0] == "filenotfound")
                    {
                        //return "file_not_found:" + pathTags.Split('|')[1];

                        //si le fichier n'existe pas, on construit le chemin pour l'enregistrer
                        pathTags = physiquepath.Replace("[idstructure]", strStructureId).Replace("[siteName]", fileNameToSave).Replace("[.lang]", "." + langue);
                    }



                    if (pathTags.Split('|')[0] == "error")
                    {
                        return pathTags.Split('|')[1];
                    }

                    GestionFichiers.SaveFile(pathTags, _text);
                }
                else
                {

                    string fileNameToSave = _pageName + _direction;


                    if(_buyerProfilId > 0)
                    {

                        fileNameToSave = _pageName + _direction+".pa"+_buyerProfilId;
                    }
                    if (IsWidgetModeSettings(indivSettingsJson) && (indivSettingsJson != null && indivSettingsJson.basket.isWidget.Value && _pageName.ToLower().Contains("fpaniers")))
                    {
                        if (_buyerProfilId > 0)
                        {
                            fileNameToSave =  "infofpanierSWidget.pa" + _buyerProfilId;
                        }
                        else
                        {
                            fileNameToSave = "infofpanierSWidget" + _direction;
                        }
                    }

                    if (IsWidgetModeSettings(indivSettingsJson) && (indivSettingsJson != null && indivSettingsJson.basket.isWidget.Value && _pageName.ToLower().Contains("fchoixseance")))
                    {
                        if (_buyerProfilId > 0)
                        {
                            fileNameToSave =  "infofChoixSeanceWidget.pa" + _buyerProfilId;
                        }
                        else
                        {
                            fileNameToSave = "infofChoixSeanceWidget" + _direction;
                        }
                    }

                 

                    //Cas customer area
                    if (_pageName.Contains("[region]"))
                    {
                        fileNameToSave = _pageName.Replace("[region]", _direction);
                    }

                    string pathTags = GestionCommentaires.GetPathFile("FilesCommentaire", strStructureId, fileNameToSave, physiquepath, langue);


                    //Si le fichier commentaire avec la structure existe, on le déplace dans un dossier nommé svg
                    if (File.Exists(pathTags))
                    {
                        // prends le fichier et le deplace dans le dossier SVG en renommant le fichier avec un datetime
                        GestionFichiers.MoveFile(pathTags, "\\svg");
                    }
                    else if (pathTags.Split('|')[0] == "filenotfound")
                    {
                        //return "file_not_found:" + pathTags.Split('|')[1];

                        //si le fichier n'existe pas, on construit le chemin pour l'enregistrer
                        pathTags = physiquepath.Replace("[idstructure]", strStructureId).Replace("[siteName]", fileNameToSave).Replace("[.lang]", "." + langue);
                    }


                    if (pathTags.Split('|')[0] == "error")
                    {
                        return pathTags.Split('|')[1];
                    }

                    GestionFichiers.SaveFile(pathTags, _text);

                }



                return "success:success_commentaire_insert";

            }
            return "danger:aucune_structure_selectionnee";
        }


        [WebMethod]
        [ScriptMethod(ResponseFormat = ResponseFormat.Json)]
        public static string GetContentPage(string _pageName, string _divName, string _ddlLang, string _appKey, int _manifId, int _seanceId, int _eventGroupId, int _buyerProfilId)
        {
            MyConfigurationManager.SetUserLanguage();

            if (System.Web.HttpContext.Current.Session["idstructure"] != null)
            {
                wsThemisAdmin2010.WSAdminClient wsAdmin = new wsThemisAdmin2010.WSAdminClient();

                string strStructureId = (string)System.Web.HttpContext.Current.Session["idstructure"];
                log.Debug("GetContentPage - structure : " + strStructureId);

                string infoparamHaut = @"<p class='alert alert-info' data-trad='aucun_commentaire'> </p>";
                string infoparamBas = @"<p class='alert alert-info' data-trad='aucun_commentaire'> </p>";
                string divName = (_divName.Contains("#")) ? _divName.Substring(1) : _divName;
                log.Debug("divName : " + divName);



                string physiquepath = MyConfigurationManager.AppSettings("FilesCommentaire" + _appKey);
                log.DebugFormat("Get PhysicalPathOfTheSite: {0} : ", physiquepath);

                string langue = "";
                if (string.IsNullOrEmpty(_ddlLang))
                {
                    langue = MyConfigurationManager.GetUserLanguage();
                }
                else
                {
                    langue = _ddlLang;
                }

                log.Debug("langue : " + langue);


                string strFinal = GestionTemplate.ReadFileTemplates("FilesCommentaireTemplate", langue, strStructureId);


                bool isModeCalendar = wsAdmin.IsModeCalendarEvent(strStructureId, _manifId);

                if (isModeCalendar && _pageName.Contains("fchoixseance"))
                {
                    _pageName = "infofChoixSeanceOnMultiSeance";
                }


                dynamic indivSettingsJson = GestionAppsettingsJson.LoadAppSettingsJson(int.Parse(strStructureId), 0, "INDIV", "physicalPathOfSettingsJSON", false);


                if (IsWidgetModeSettings(indivSettingsJson) && (indivSettingsJson != null && ((indivSettingsJson.sessionsList.isWidget.Value && _pageName.ToLower().Contains("fchoixseance")) || (indivSettingsJson.basket.isWidget.Value && _pageName.ToLower().Contains("fpaniers")))))
                {
                    _pageName = _pageName + "Widget";
                }


                string fileHaut = string.Empty;
                if (_seanceId > 0)
                {
                    if (_buyerProfilId > 0)
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut.grp" + _eventGroupId + "." + _manifId + "." + _seanceId + ".pa" + _buyerProfilId.ToString(), physiquepath, langue, _buyerProfilId);
                    }
                    else if (_eventGroupId > 0)
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut.grp" + _eventGroupId + "." + _manifId + "." + _seanceId, physiquepath, langue, _buyerProfilId);
                    }
                    else
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut." + _manifId + "." + _seanceId, physiquepath, langue, _buyerProfilId);
                    }
                }

                else if (_manifId > 0)
                {

                    if(_buyerProfilId > 0)
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut.grp" + _eventGroupId + "." + _manifId + ".pa" + _buyerProfilId.ToString(), physiquepath, langue, _buyerProfilId);
                    }
                    else if (_eventGroupId > 0)
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut.grp" + _eventGroupId + "." + _manifId, physiquepath, langue, _buyerProfilId);

                    }
                    else
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut." + _manifId, physiquepath, langue, _buyerProfilId);
                    }
                }
                else if (_eventGroupId > 0)
                {

                    if (_buyerProfilId > 0)
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut.grp" + _eventGroupId+ ".pa" + _buyerProfilId, physiquepath, langue, _buyerProfilId);
                    }
                    else
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut.grp" + _eventGroupId, physiquepath, langue, _buyerProfilId);
                    }
                }
                else if (_buyerProfilId > 0 )
                {

                    if(_eventGroupId> 0)
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut.grp" + _eventGroupId+".pa"+_buyerProfilId.ToString(), physiquepath, langue, _buyerProfilId);
                    }
                    else
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut.pa" + _buyerProfilId, physiquepath, langue, _buyerProfilId);
                    }
                }
                else
                {
                    //Cas customer area
                    if (_pageName.Contains("[region]"))
                    {
                        string tmpPageName = _pageName.Replace("[region]", "haut");
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, tmpPageName, physiquepath, langue, _buyerProfilId);
                    }
                    else
                    {
                        fileHaut = GestionCommentaires.ReadFile(strStructureId, _pageName + "haut", physiquepath, langue, _buyerProfilId);
                    }
                }

                if (fileHaut.Split('|')[0] != "filenotfound")
                {
                    strFinal = strFinal.Replace("[content_haut]", fileHaut).Replace("[info_param_haut]", string.Empty).Replace("[divName]", divName);
                }
                else
                {
                    strFinal = strFinal.Replace("[content_haut]", string.Empty).Replace("[info_param_haut]", infoparamHaut).Replace("[divName]", divName);
                }


                string filebas = string.Empty;

                if (_seanceId > 0)
                {
                    if (_buyerProfilId > 0)
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas.grp" + _eventGroupId + "." + _manifId + ".pa" + _buyerProfilId.ToString(), physiquepath, langue, _buyerProfilId);
                    }
                    else if (_eventGroupId > 0)
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas.grp" + _eventGroupId + "." + _manifId + "." + _seanceId, physiquepath, langue, _buyerProfilId);
                    }
                    else
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas." + _manifId + "." + _seanceId, physiquepath, langue, _buyerProfilId);
                    }
                }

                else if (_manifId > 0)
                {
                    if (_buyerProfilId > 0)
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas.grp" + _eventGroupId + "." + _manifId + ".pa" + _buyerProfilId.ToString(), physiquepath, langue, _buyerProfilId);
                    }
                    else if (_eventGroupId > 0)
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas.grp" + _eventGroupId + "." + _manifId, physiquepath, langue, _buyerProfilId);
                    }
                    else
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas." + _manifId, physiquepath, langue, _buyerProfilId);

                    }
                }
                else if (_eventGroupId > 0)
                {

                    if (_buyerProfilId > 0)
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas.grp" + _eventGroupId + ".pa" + _buyerProfilId, physiquepath, langue, _buyerProfilId);
                    }
                    else
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas.grp" + _eventGroupId, physiquepath, langue, _buyerProfilId);
                    }

                }
                else if (_buyerProfilId > 0)
                {

                    if (_eventGroupId > 0)
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas.grp" + _eventGroupId + ".pa" + _buyerProfilId.ToString(), physiquepath, langue, _buyerProfilId);
                    }
                    else
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas.pa" + _buyerProfilId, physiquepath, langue, _buyerProfilId);
                    }
                }
                else
                {

                    //Cas customer area
                    if (_pageName.Contains("[region]"))
                    {
                        string tmpPageName = _pageName.Replace("[region]", "bas");
                        filebas = GestionCommentaires.ReadFile(strStructureId, tmpPageName, physiquepath, langue, _buyerProfilId);
                    }
                    else
                    {
                        filebas = GestionCommentaires.ReadFile(strStructureId, _pageName + "bas", physiquepath, langue, _buyerProfilId);
                    }
                }

                if (filebas.Split('|')[0] != "filenotfound")
                {
                    strFinal = strFinal.Replace("[content_bas]", filebas).Replace("[info_param_bas]", string.Empty).Replace("[divname]", divName);
                }
                else
                {
                    strFinal = strFinal.Replace("[content_bas]", string.Empty).Replace("[info_param_bas]", infoparamBas).Replace("[divname]", divName);
                }

                return strFinal;
            }

            return "danger:aucune_structure_selectionnee";
        }


        private static bool IsWidgetModeSettings(dynamic indivSettingsJson)
        {

            bool isWidgetMode = false;
            if (indivSettingsJson != null)
            {
                if (indivSettingsJson.sessionsList != null && indivSettingsJson.basket != null)
                {

                    if (indivSettingsJson.sessionsList.isWidget != null && indivSettingsJson.basket.isWidget != null)
                    {

                        if (indivSettingsJson.sessionsList.isWidget.Value || indivSettingsJson.basket.isWidget.Value)
                        {
                            isWidgetMode = true;
                        }
                    }

                }

            }

            return isWidgetMode;
        }
    }
}