# Test de la correction des commentaires

## Problème identifié
Quand on sélectionne une manifestation avec un événement ou avec un événement et une séance, les commentaires ne sont pas récupérés après sauvegarde.

## Cause du problème
Incohérence entre les méthodes utilisées pour la sauvegarde et la récupération :
- **Sauvegarde** : `GestionFichiers.GetPathFile`
- **Récupération** : `GestionCommentaires.ReadFile` → `GestionCommentaires.GetFileCommentaires`

## Solution appliquée
Modification de la méthode `GetContentPage` dans `commentaires_pages.aspx.cs` pour utiliser la même logique que la sauvegarde :
- Remplacé `GestionCommentaires.ReadFile` par `GestionFichiers.GetPathFile` + `GestionFichiers.ReadFile`
- Ajouté des logs de débogage

## Test à effectuer

### Étape 1 : Test avec manifestation seule (devrait déjà fonctionner)
1. Sélectionner un groupe de manifestation sans sélectionner d'événement ni de séance
2. Ajouter un commentaire haut et bas
3. Enregistrer
4. Recharger la page → Le commentaire devrait être récupéré ✓

### Étape 2 : Test avec manifestation + événement (problème corrigé)
1. Sélectionner un groupe de manifestation
2. Sélectionner un événement (manifestation)
3. Ajouter un commentaire haut et bas
4. Enregistrer
5. Recharger la page → Le commentaire devrait maintenant être récupéré ✓

### Étape 3 : Test avec manifestation + événement + séance (problème corrigé)
1. Sélectionner un groupe de manifestation
2. Sélectionner un événement (manifestation)
3. Sélectionner une séance
4. Ajouter un commentaire haut et bas
5. Enregistrer
6. Recharger la page → Le commentaire devrait maintenant être récupéré ✓

## Logs de débogage
Les logs suivants ont été ajoutés pour tracer les noms de fichiers :
- `SaveContent - Sauvegarde fichier avec eventGroupId : [nom_fichier]`
- `GetContentPage - Recherche fichier haut avec eventGroupId : [nom_fichier]`
- `GetContentPage - Recherche fichier bas avec eventGroupId : [nom_fichier]`

Ces logs permettront de vérifier que les mêmes noms de fichiers sont utilisés pour la sauvegarde et la récupération.
